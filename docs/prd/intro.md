## Introduction

### **2.1. 项目概述**

本文档旨在明确“知深学习导师”项目在 MVP 阶段的核心目标、功能范围、用户需求和成功标准。其核心是验证一个核心假设：**一个具备长期主题记忆、且能通过交互式摘要轻松回顾的 AI 对话体验，是否能显著提升用户的学习效率和深度，并让用户认为其价值远超于普通聊天机器人。**

### **2.2. 核心用户故事**

“作为一名需要持续学习新领域的知识工作者，**我希望** 能将一份或多份复杂的资料‘喂’给一个AI导师，然后通过轻松、无压力的对话来逐步消化它。**我希望能** 随时中断和继续学习，而系统能记住我们聊到哪里了。**当我完成后**，系统应该已经自动为我整理好了一份可随时回顾、并能链接回原始对话的精炼笔记，这样我就真正地把知识‘装进’了脑子里，而不只是‘收藏’了一个文档。”

### **2.3. 功能需求 (Functional Requirements - P0)**

- **F1 - 主题式会话管理 (Topic-based Conversations):**
  - **F1.1:** 用户可以创建一个新的、独立的主题会话。
  - **F1.2:** 用户在创建主题时，可以关联一个或多个知识源，支持以下方式：
    - **a. 文件上传:** 支持 `.txt`, `.md` 格式。若上传多个文件，后台将其合并为一个统一的知识源处理。
    - **b. 长文本粘贴:** 若在输入框中粘贴长文本，系统应能识别并询问是否将其作为本次学习的基础材料。
  - **F1.3:** 应用主界面以列表形式展示所有历史主题，用户可以清晰地区分和选择。
  - **F1.4:** 用户可以点击任意一个历史主题，无缝载入上次的对话历史和摘要，继续学习。
- **F2 - 双栏专注学习界面 (Dual-Pane Interface):**
  - **F2.1:** 左侧为核心对话区域，展示用户与AI的完整交互历史。
  - **F2.2:** 右侧为“对话流式摘要”区域，以保留了对话感的“你/AI”格式，实时展示摘要。
- **F3 - 核心交互循环 (Core Interaction Loop):**
  - **F3.1:** 当一个“逻辑回合”（从用户发起新提问到AI完成所有相关回答）结束后，后端异步触发摘要任务。
  - **F3.2:** 摘要任务的输出，必须是一个结构化的对象，例如：{ "user_summary": "用户本轮的核心观点或问题", "assistant_summary": "AI本轮的核心回应" }
  - **F3.3:** 前端收到该对象后，在右侧摘要区域渲染出两条连续的、带有身份标识的摘要。
  - **F3.4:** 点击这两条摘要中的任意一条，左侧对话区域必须能平滑滚动到对应“逻辑回合”的起始消息处，并高亮该回合的所有消息。
- **F4 - Manticore 驱动的智能上下文引擎 (Manticore-powered Context Engine):**
  - **F4.1:** **数据持久化:** 所有用户的对话记录、AI生成的摘要、以及上传文档的分块，都必须持久化存储在 Manticore 数据库中。
  - **F4.2:** **主题内记忆:** 当用户在一个特定主题内继续对话时，系统向大模型发送的 Prompt 必须动态构建，包含：固定人设、短期记忆（最近消息）、长期记忆锚点（从当前主题的历史摘要中搜索）、以及知识库（从关联文档的切片中搜索）。

### **2.4. MVP 范围之外 (Out of Scope)**

- 全局上下文搜索。
- 复杂文档格式支持（PDF, Word等）。
- 高级用户账户管理与分享功能。
- 移动端原生 App。
